/* Main container with enhanced gradient background */
.login-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60dvh;
  min-height: 500px;
  padding: 2rem;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
}

/* Enhanced left side with animations */
.login-content-left {
  font-size: 1dvw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  width: 50%;
  height: 100%;
  padding: 2rem 1rem;
  position: relative;
  z-index: 2;
}

/* Logo with subtle glow (no floating animation) */
.login-content-left img {
  width: 9dvw;
  min-width: 120px;
  filter: drop-shadow(0 0 16px rgba(61, 240, 255, 0.45));
  transition: transform 0.25s ease, filter 0.25s ease;
}

.login-content-left img:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 0 30px rgba(0, 255, 255, 0.8));
}

/* Animated feature tags */
.login-content-left > div {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin: 1rem 0;
  animation: slideInLeft 1s ease-out;
}

.login-content-left > div:nth-child(3) {
  animation-delay: 0.3s;
}

.login-content-left > div:nth-child(4) {
  animation-delay: 0.6s;
}

.login-content-left span {
  padding: 0.8rem 1.5rem;
  margin: 0.3rem;
  line-height: 1.5;
  color: white;
  background: rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.28);
  backdrop-filter: blur(10px) saturate(120%);
  -webkit-backdrop-filter: blur(10px) saturate(120%);
  border-radius: 14px;
  font-weight: 500;
  transition: transform 0.2s ease, box-shadow 0.2s ease, background 0.2s ease, border-color 0.2s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.login-content-left span::before {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(120px 60px at var(--mx, -20%) var(--my, 50%), rgba(255,255,255,0.25), transparent 60%);
  opacity: 0;
  transition: opacity 0.25s ease;
}

.login-content-left span:hover {
  transform: translateY(-2px);
  border-color: rgba(72, 217, 255, 0.8);
  box-shadow: 0 8px 20px rgba(72, 217, 255, 0.25);
  background: rgba(72, 217, 255, 0.18);
}

.login-content-left span:hover::before {
  opacity: 1;
}

/* AI Introduction Section - glass-morphism for readability */
.ai-introduction {
  margin-top: auto;
  text-align: center;
  color: rgba(255,255,255,0.95);
  padding: 1.2rem 1.4rem;
  background: linear-gradient(180deg, rgba(255,255,255,0.18), rgba(255,255,255,0.1));
  border: 1px solid rgba(255, 255, 255, 0.35);
  backdrop-filter: blur(16px) saturate(120%);
  -webkit-backdrop-filter: blur(16px) saturate(120%);
  border-radius: 16px;
  animation: fadeInUp 1.1s ease-out 0.5s both;
  position: relative;
  overflow: hidden;
}

.ai-introduction::before {
  content: '';
  position: absolute;
  inset: -1px;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(90deg, rgba(28, 177, 134, 0.4), rgba(61, 240, 255, 0.4), rgba(93, 129, 255, 0.4));
  /* masks for gradient border cutout */
  -webkit-mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  -webkit-mask-composite: xor;
          mask-composite: exclude;
}

.ai-introduction h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  background: linear-gradient(45deg, #1cb186, #3df0ff, #5d81ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.ai-introduction p {
  margin: 0;
  font-size: 0.92rem;
  opacity: 0.92;
  line-height: 1.5;
}

/* Enhanced right side login form */
.login-content-right {
  display: flex;
  flex-direction: column;
  width: 50%;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideInRight 1s ease-out;
  position: relative;
  overflow: hidden;
}

.login-content-right::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #1cb186, #3df0ff, #5d81ff);
  animation: shimmer 2s ease-in-out infinite;
}

.login-content-right > div:first-child {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #1cb186, #3df0ff, #5d81ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: textGlow 2s ease-in-out infinite alternate;
}

.login-content-right > div:nth-child(2) {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 2rem;
  line-height: 1.5;
  opacity: 0.8;
}

/* Enhanced input group styling */
.login-content-right .el-input-group {
  margin-bottom: 1.5rem;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.login-content-right .el-input-group:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(67, 101, 255, 0.2);
}

/* Enhanced button styling */
.login-content-right button {
  background: linear-gradient(45deg, #1cb186, #3df0ff, #5d81ff);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(67, 101, 255, 0.3);
}

.login-content-right button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.login-content-right button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(67, 101, 255, 0.4);
}

.login-content-right button:hover::before {
  left: 100%;
}

.login-content-right button:active {
  transform: translateY(-1px);
}

/* Enhanced checkbox styling */
.login-content-right .el-checkbox {
  margin-top: 0.5rem;
}

.policy-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 6px;
}

.login-content-right .el-checkbox__label {
  color: #6b7b7c;
  font-size: 0.9rem;
  line-height: 1.5;
}

.policy-link {
  color: #1cb186;
  text-decoration: none;
  border-bottom: 1px dashed rgba(28, 177, 134, 0.5);
  transition: color 0.2s ease, border-color 0.2s ease;
}

.policy-link:hover {
  color: #3df0ff;
  border-color: rgba(61, 240, 255, 0.7);
}

.login-content-right .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #4365ff;
  border-color: #4365ff;
}

.login-content-right .el-checkbox__input:hover .el-checkbox__inner {
  border-color: #00ffff;
}

/* Phone area code styling */
.login-phone-area-code {
  float: left;
  font-weight: 500;
}

.login-phone-area-place {
  float: right;
  color: var(--el-text-color-secondary);
  font-size: 0.8rem;
  opacity: 0.7;
}

/* Animations */
@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes shimmer {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

@keyframes textGlow {
  from { text-shadow: 0 0 5px rgba(67, 101, 255, 0.3); }
  to { text-shadow: 0 0 15px rgba(67, 101, 255, 0.6); }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    height: auto;
    min-height: 80vh;
    padding: 1rem;
  }

  .login-content-left,
  .login-content-right {
    width: 100%;
    margin-bottom: 1rem;
  }

  .login-content-left {
    height: auto;
    padding: 1rem;
  }

  .login-content-left img {
    width: 80px;
    min-width: 80px;
  }

  .login-content-left span {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .login-content-right {
    padding: 1.5rem;
  }

  .login-content-right > div:first-child {
    font-size: 1.2rem;
  }

  .ai-introduction {
    padding: 1rem;
  }

  .ai-introduction h3 {
    font-size: 1rem;
  }

  .ai-introduction p {
    font-size: 0.8rem;
  }
}
