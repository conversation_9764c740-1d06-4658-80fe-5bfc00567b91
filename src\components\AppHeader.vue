<script lang="ts" setup>
import { useRouter } from "vue-router";

const router = useRouter();
const goToHome = () => {
  router.push("/");
};
</script>

<template>
  <div class="app-header">
    <img src="/text_logo_cn.png" alt="logo" @click="goToHome" />
    <nav class="navbar">
      <div class="nav-links">
        <a href="/">首页</a>
        <a href="/vet-assistant">宠物助手</a>
        <a href="/vet-open">开放平台</a>
        <a href="/vet-open">晓闻官网</a>
        <a href="#">关于我们</a>
      </div>
    </nav>
    <div class="app-header-user">
      <el-avatar
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        style="width: 4.7dvh; height: 4.7dvh"
      />
    </div>
  </div>
</template>

<style scoped src="./AppHeader.css"></style>
