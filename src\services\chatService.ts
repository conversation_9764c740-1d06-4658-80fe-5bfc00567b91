import { fetchEventSource } from '@microsoft/fetch-event-source'
import api from '../utils/api'

// 聊天消息类型
export interface ChatMessage {
  id: number
  type: 'user' | 'ai' | string
  content: string
  thinking?: string
  timestamp?: number
  loading?: boolean
  thinkLoading?: boolean
}

// 发送消息的请求参数
export interface SendMessageRequest {
  message: string
  sessionId?: string
}

// AI 回复的响应数据
export interface SendMessageResponse {
  reply: string
  sessionId: string
  messageId: number
}

// 流式响应的数据块
export interface StreamChunk {
  content: string
  thinking: string
  finished: boolean
  thinkFinished: boolean
  messageId: number
}

interface ChatMessageResponse {
  items: {
    id: number;
    content: string;
    conversation_id: number;
    created_at: string;
    updated_at: string;
    status: number;
    role: number;
    message_type?: number;
    agent_id?: number;
    attachments?: unknown[];
    error_message?: string;
    has_ocr_content?: boolean;
    message_metadata?: unknown;
    ocr_result?: unknown;
    parent_message_id?: number | null;
    processing_time_ms?: number | null;
    retry_count?: number;
    sequence_number?: number;
    token_usage?: number | null;
  }[],
  pagination: { "page": number, "pages": number, "size": number, "total": number }

}

/**
 * 聊天服务类
 */
export class ChatService {
  // 用于中止请求的控制器
  private static abortController: AbortController | null = null
  // 重试计数器
  private static retryCount: number = 0
  // 最大重试次数
  private static readonly MAX_RETRIES = 3

  /**
   * 中止当前的流式请求
   */
  static abortCurrentRequest(): void {
    if (this.abortController) {
      this.abortController.abort()
      this.abortController = null
    }
  }

  /**
   * 发送消息并获取流式响应
   * 这个方法用于实现打字机效果
   */
  static async sendMessageStream(
    params: SendMessageRequest,
    onChunk: (chunk: StreamChunk) => void
  ): Promise<void> {
    const {  message } = params
    // 创建新的中止控制器
    this.abortController = new AbortController()
    // 重置重试计数器
    this.retryCount = 0

    let totalContent = ''
    let totalThinking = ''
    let thinkFinished = false
    let attachmentMessage = ""

    try {
      await fetchEventSource('api/system-agents/diagnosis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Authorization': getAuthorizationHeader(),
        },
        signal: this.abortController.signal,
        body: JSON.stringify({
          "messages": [
            {
              "role": "user",
              "content": attachmentMessage + (message ?? "根据报告信息进行分析")
            }
          ],
          "stream": true
        }),
        async onopen(response) {
          if (response.ok) {
            // 连接成功，重置重试计数器
            ChatService.retryCount = 0
            return // 一切正常
          } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
            // 客户端错误，不重试
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          } else {
            // 服务器错误或其他问题，可能会重试
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
        },
        onmessage(event) {
          const jsonData = JSON.parse(event.data)
          // 检查是否是结束标记
          if (jsonData.event === 'end') {
            return
          }
          // 根据实际的 API 响应格式提取内容
          let content = ''

          if (jsonData.content) {
            content = jsonData.content
          }

          if (content) {
            if (content === '</think>') {
              thinkFinished = true
              return
            }
            if (thinkFinished) {
              totalContent += content
            } else {
              totalThinking += content
            }

            // 发送增量更新
            onChunk({
              content: totalContent,
              thinking: totalThinking,
              thinkFinished: thinkFinished,
              finished: false,
              messageId: Date.now()
            })
          }
        },
        onerror(error) {
          const httpCode: number = parseInt(error.message.split(':')[0].replace("HTTP", "").trim())
          console.error('SSE连接错误:', error, '重试次数:', ChatService.retryCount)
          // 检查重试次数
          if (ChatService.retryCount >= ChatService.MAX_RETRIES) {
            console.error('已达到最大重试次数，停止重试')
            throw new Error('连接失败，请稍后重试')
          }

          if (httpCode >= 400 && httpCode < 500 && httpCode !== 429) {
            throw error
          } else if (isNaN(httpCode)) {
            throw new Error('处理错误，请稍候重试')
          }
          ChatService.retryCount++
        },
        onclose() {
          // 连接关闭时确保发送最终完成状态
          if (totalContent && totalThinking) {
            onChunk({
              content: totalContent,
              thinking: totalThinking,
              finished: true,
              thinkFinished: true,
              messageId: Date.now()
            })
          }
        }
      })
    } finally {
      // 清理中止控制器
      this.abortController = null
    }
  }

  /**
   * 获取聊天历史
   * @param page 页码，从1开始
   * @param size 每页数量，默认10条
   */
  static async getChatHistory(page: number = 1): Promise<any> {
    const response = await api.get<any>(`api/conversations/${getConversationId()}/messages`,
      {
        order_by: 'created_at',
        order_desc: true,
        size: 10,
        page
      },
      {
        headers: {
          'Authorization': getAuthorizationHeader()
        }
      })
    const result: ChatMessageResponse = response.data
    return result
  }
}

// 默认导出
export default ChatService
