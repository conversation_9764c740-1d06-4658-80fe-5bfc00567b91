<script lang="ts" setup>
import { AREA_CODE } from "@/constants/constant";
import { useCommonStore } from "@/stores/common";
import { ref } from "vue";

//Pina库
const commonStore = useCommonStore();
const curAreaCode = ref(AREA_CODE[0].value);
const curPhone = ref();
const isAllow = ref(false);
</script>

<template>
  <el-dialog
    v-model="commonStore.isLogin"
    :show-close="false"
    width="60%"
    style="
      background:
        white
        linear-gradient(
          to right,
          #1cb186 0%,
          #1fc296 12%,
          #2bd3ab 25%,
          #3df0ff 40%,
          #48d9ff 60%,
          #538bff 80%,
          #5d81ff 100%
        );
    "
  >
    <div class="login-content">
      <div class="login-content-left">
        <img src="/logo.png" alt="logo" />
        <div>
          <span>智能问诊</span>
          <span>知识查询</span>
        </div>
        <div>
          <span>语音录入</span>
          <span>报告分析</span>
          <span>影像诊断</span>
        </div>
        <div class="ai-introduction">
          <h3>🤖 AI 兽医助手</h3>
          <p>
            基于先进的人工智能技术，为您提供专业的兽医诊断支持和智能化医疗服务体验
          </p>
        </div>
      </div>
      <div class="login-content-right">
        <div>登录后免费试用完整功能</div>
        <div>辅助鉴别诊断 AI知识助手 语音病历录入 影像诊断助手</div>
        <el-input-group style="display: flex">
          <el-select v-model="curAreaCode">
            <el-option
              v-for="item in AREA_CODE"
              :key="item.key"
              :label="item.value"
              :value="item.value"
            >
              <span class="login-phone-area-code">{{ item.value }}</span>
              <span class="login-phone-area-place">{{ item.label }}</span>
            </el-option>
          </el-select>
          <el-input v-model="curPhone" placeholder="请输入手机号" />
        </el-input-group>
        <button>下一步</button>
        <el-checkbox v-model="isAllow" class="policy-checkbox">
          我已阅读并同意
          <a href="/terms" target="_blank" rel="noopener" class="policy-link">《用户协议》</a>
          和
          <a href="/privacy" target="_blank" rel="noopener" class="policy-link">《隐私政策》</a>
        </el-checkbox>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped src="./LoginDialog.css"></style>
