.app-header {
  height: var(--app-header-height);
  display: grid;
  grid-template-columns: 1fr 4fr 1fr;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
}

.app-header > img {
  height: var(--app-header-height);
  cursor: pointer;
}

.nav-links {
  display: flex;
  gap: 2dvw;
  a {
    color: rgba(255, 255, 255, 0.86);
    text-decoration: none;
    font-size: 0.9dvw;
    font-weight: bold;
    letter-spacing: 0.1dvw;
    padding: 0.4dvw 0.8dvw;
    border-radius: 0.4dvw;
    border: 0.15dvw solid transparent;
    transition:
      color 0.18s ease,
      text-shadow 0.18s ease;
  }
  a:hover {
    background-color: transparent;
    box-shadow: none;
    border-color: transparent;
    color: #fff;
    text-shadow:
      0 0 0.2dvw rgba(255, 255, 255, 0.65),
      0 0 0.4dvw rgba(236, 179, 255, 0.4),
      0 0 0.6dvw rgba(236, 179, 255, 0.25);
    cursor: pointer;
  }
}

.app-header-user {
  text-align: right;
  padding-right: 1.2dvw;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
