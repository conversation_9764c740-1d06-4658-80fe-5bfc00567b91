<template>
  <div style="display: flex; flex-direction: column; height: 100dvh">
    <AppHeader style="flex: 0" />
    <router-view style="flex: 1" />
    <LoginDialog />
  </div>
</template>

<script lang="ts" setup>
import AppHeader from "@/components/AppHeader.vue";
import LoginDialog from "./components/LoginDialog.vue";
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
body {
  margin: 0;
  background: rgb(48, 51, 59);
}
</style>
