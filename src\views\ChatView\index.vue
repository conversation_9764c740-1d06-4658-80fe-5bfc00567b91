<script lang="ts" setup>
import ImageDiagnosisIcon from "@/components/icons/ImageDiagnosisIcon.vue";
import LabReportIcon from "@/components/icons/LabReportIcon.vue";
import NewChatIcon from "@/components/icons/NewChatIcon.vue";
import { FUNCTION_LIST, type FunctionItem } from "@/constants/constant";
import type { ConversationInfo } from "@/services/conversationService";
import { useConversationStore } from "@/stores/conversation";
import { onMounted, ref } from "vue";
import type {
  BubbleListItemProps,
  BubbleListProps,
} from "vue-element-plus-x/types/BubbleList";

//Pina库
const conversationStore = useConversationStore();

onMounted(async () => {
  await conversationStore.getConversationList();
});

const cur_function = ref(FUNCTION_LIST[0]);
const cur_history = ref();
const senderValue = ref("");
type listType = BubbleListItemProps & {
  key: number;
  role: "user" | "ai";
};

const list: BubbleListProps<listType>["list"] = generateFakeItems(5);
function generateFakeItems(count: number): listType[] {
  const messages: listType[] = [];
  for (let i = 0; i < count; i++) {
    const role = i % 2 === 0 ? "ai" : "user";
    const placement = role === "ai" ? "start" : "end";
    const key = i + 1;
    const content =
      role === "ai"
        ? "💖 感谢使用 Element Plus X ! 你的支持，是我们开源的最强动力 ~".repeat(
            10
          )
        : `哈哈哈，让我试试`;
    const loading = false;
    const shape = "corner";
    const variant = role === "ai" ? "filled" : "outlined";
    const isMarkdown = false;
    const typing = role === "ai" ? i === count - 1 : false;
    const avatar =
      role === "ai"
        ? "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        : "https://avatars.githubusercontent.com/u/76239030?v=4";

    messages.push({
      key, // 唯一标识
      role, // user | ai 自行更据模型定义
      placement, // start | end 气泡位置
      content, // 消息内容 流式接受的时候，只需要改这个值即可
      loading, // 当前气泡的加载状态
      shape, // 气泡的形状
      variant, // 气泡的样式
      isMarkdown, // 是否渲染为 markdown
      typing, // 是否开启打字器效果 该属性不会和流式接受冲突
      isFog: role === "ai", // 是否开启打字雾化效果，该效果 v1.1.6 新增，且在 typing 为 true 时生效，该效果会覆盖 typing 的 suffix 属性
      avatar,
      avatarSize: "24px", // 头像占位大小
      avatarGap: "12px", // 头像与气泡之间的距离
    });
  }
  return messages;
}

function clickFunction(item: FunctionItem) {
  cur_function.value = item;
  cur_history.value = null;
}

function clickHistory(item: ConversationInfo) {
  cur_history.value = item;
  conversationStore.getChatHistory(item.conversation_id);
}
</script>

<template>
  <div class="chat-view">
    <div class="chat-slider">
      <div class="chat-function">
        <div>
          <h3>功能模块</h3>
        </div>
        <ul class="chat-function-list">
          <li
            v-for="item in FUNCTION_LIST"
            :key="item.id"
            @click="clickFunction(item)"
            :class="{ active: cur_function.id === item.id }"
          >
            <component :is="item.icon" />
            {{ item.title }}
          </li>
        </ul>
      </div>
      <div class="chat-history">
        <div class="chat-history-title">
          <h3>历史对话</h3>
          <el-icon @click="cur_history = null" style="font-size: 0.8dvw">
            <Plus />
          </el-icon>
        </div>
        <ul class="chat-history-list">
          <li
            v-for="item in conversationStore.conversationList"
            :key="item.id"
            @click="clickHistory(item)"
            :class="{ active: cur_history?.id === item.id }"
          >
            {{ item.title }}
          </li>
        </ul>
      </div>
    </div>
    <div class="chat-content">
      <div class="chat-content-header">
        <h3>{{ cur_function.title }}</h3>
        <div
          v-if="cur_history && cur_history.title"
          class="chat-content-header-subtitle"
        >
          （{{ cur_history.title }}）
        </div>
        <button class="new-chat-button" @click="cur_history = null">
          <NewChatIcon
            style="
              font-size: 0.8dvw;
              margin-right: 0.4dvw;
              transform: translateY(0.1dvw);
            "
          />
          新对话
        </button>
      </div>
      <div class="chat-content-main">
        <div v-if="!cur_history" class="chat-welcome">
          <img src="/logo.png" alt="Logo" class="welcome-logo" />
          <h2 class="welcome-title">{{ cur_function.subTitle }}</h2>
          <p class="welcome-subtitle">
            {{ cur_function.description }}
          </p>
        </div>
        <div v-else class="chat-bubble-container">
          <BubbleList :list="list" />
        </div>
        <MentionSender
          class="chat-input"
          v-model="senderValue"
          :auto-size="{ minRows: 3, maxRows: 6 }"
          :inputStyle="{ color: 'white' }"
        >
          <template #footer>
            <button class="chat-input-other-button">
              <LabReportIcon />
              <span style="margin-left: 0.2dvw">化验结果</span>
            </button>
            <button class="chat-input-other-button">
              <ImageDiagnosisIcon />
              <span style="margin-left: 0.2dvw">影像报告</span>
            </button>
          </template>
        </MentionSender>
      </div>
    </div>
  </div>
</template>
<style scoped src="./index.css"></style>
