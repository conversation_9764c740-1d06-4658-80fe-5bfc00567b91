.chat-view {
  display: grid;
  grid-template-columns: 1fr 4fr;
}

.chat-slider {
  background: linear-gradient(
    to bottom,
    rgba(115, 178, 255, 0.13) 0%,
    rgba(217, 182, 255, 0.13) 70%
  );
  height: 100%;
  > div {
    padding: 1.2dvw 1dvw;
  }
  .chat-function {
    list-style: none;
    padding-left: 0;
    margin: 0;
    li {
      padding: 0.5dvw;
      font-size: 0.9dvw;
      line-height: 1.2dvw;
      margin-bottom: 0.5dvw;
      color: var(--el-text-color-regular);
      display: flex;
      align-items: center;
      gap: 0.6dvw;
    }
  }
  .chat-history {
    list-style: none;
    padding-left: 0;
    margin: 0;
    li {
      padding: 0.8dvw;
      line-height: 1.2dvw;
      background-color: white;
      border-radius: 0.4dvw;
      margin-bottom: 0.5dvw;
      >p{
        font-size: 0.9dvw;
        margin: 0;
        color: var(--el-text-color-regular);
      }
      >div{
        text-align: right;
        font-size: 0.7dvw;
        color: var(--el-text-color-secondary);
      }
    }
  }
  h3 {
    margin-block: 0 1dvw;
    font-size: 1dvw;
  }
  hr {
    margin: 0;
  }
}

.chat-box {
  background-color: white;
  width: 100%;
}
